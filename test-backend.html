<!DOCTYPE html>
<html>
<head>
    <title>Test Backend Connection</title>
</head>
<body>
    <h1>🔥 Test Backend Connection</h1>
    
    <h2>Test Registration</h2>
    <button onclick="testRegister()">Test Register</button>
    
    <h2>Test Login</h2>
    <button onclick="testLogin()">Test Login</button>
    
    <h2>Test Dashboard</h2>
    <button onclick="testDashboard()">Test Dashboard (need token)</button>
    
    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; white-space: pre-wrap;"></div>
    
    <script>
        let token = '';
        
        async function testRegister() {
            const data = {
                userId: 'testuser' + Date.now(),
                name: 'Test User',
                email: 'test' + Date.now() + '@example.com',
                password: 'password123',
                dateOfBirth: '1990-01-01'
            };
            
            document.getElementById('result').innerHTML = '🔄 Testing registration...';
            
            try {
                const response = await fetch('http://localhost:3001/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                document.getElementById('result').innerHTML = 
                    '📤 REGISTER TEST:\n' + 
                    'Status: ' + response.status + '\n' +
                    'Response: ' + JSON.stringify(result, null, 2);
                
            } catch (error) {
                document.getElementById('result').innerHTML = '❌ ERROR: ' + error.message;
            }
        }
        
        async function testLogin() {
            const data = {
                emailOrUserId: '<EMAIL>',
                password: 'password123'
            };
            
            document.getElementById('result').innerHTML = '🔄 Testing login...';
            
            try {
                const response = await fetch('http://localhost:3001/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                if (result.token) {
                    token = result.token;
                }
                
                document.getElementById('result').innerHTML = 
                    '🔐 LOGIN TEST:\n' + 
                    'Status: ' + response.status + '\n' +
                    'Response: ' + JSON.stringify(result, null, 2);
                
            } catch (error) {
                document.getElementById('result').innerHTML = '❌ ERROR: ' + error.message;
            }
        }
        
        async function testDashboard() {
            if (!token) {
                document.getElementById('result').innerHTML = '❌ No token available. Please login first.';
                return;
            }
            
            document.getElementById('result').innerHTML = '🔄 Testing dashboard...';
            
            try {
                const response = await fetch('http://localhost:3001/dashboard', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json',
                    }
                });
                
                const result = await response.json();
                
                document.getElementById('result').innerHTML = 
                    '📊 DASHBOARD TEST:\n' + 
                    'Status: ' + response.status + '\n' +
                    'Response: ' + JSON.stringify(result, null, 2);
                
            } catch (error) {
                document.getElementById('result').innerHTML = '❌ ERROR: ' + error.message;
            }
        }
    </script>
</body>
</html>
