async function testBackend() {
    console.log('🔄 Testing Backend APIs...\n');

    const baseURL = 'http://localhost:3001';

    try {
        // Test 1: Health Check
        console.log('1. Testing Health Check...');
        const healthResponse = await fetch(`${baseURL}/`);
        const healthData = await healthResponse.json();
        console.log('✅ Health Check:', healthData);
        console.log('');

        // Test 2: Registration
        console.log('2. Testing Registration...');
        const registerData = {
            userId: 'testuser' + Date.now(),
            name: 'Test User',
            email: 'test' + Date.now() + '@example.com',
            password: 'password123',
            dateOfBirth: '1990-01-01'
        };

        const registerResponse = await fetch(`${baseURL}/register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(registerData)
        });
        const registerResult = await registerResponse.json();
        console.log('✅ Registration Success:', registerResult);
        console.log('');

        // Test 3: Login
        console.log('3. Testing Login...');
        const loginData = {
            emailOrUserId: registerData.email,
            password: registerData.password
        };

        const loginResponse = await fetch(`${baseURL}/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(loginData)
        });
        const loginResult = await loginResponse.json();
        console.log('✅ Login Success:', loginResult);

        const token = loginResult.token;
        console.log('🔑 Token received:', token ? 'Yes' : 'No');
        console.log('');

        // Test 4: Dashboard
        console.log('4. Testing Dashboard...');
        const dashboardResponse = await fetch(`${baseURL}/dashboard`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        const dashboardResult = await dashboardResponse.json();
        console.log('✅ Dashboard Success:', dashboardResult);

        console.log('\n🎉 All tests passed! Backend is working correctly.');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('Error details:', error);
    }
}

testBackend();
