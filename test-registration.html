<!DOCTYPE html>
<html>
<head>
    <title>🔥 SIMPLE REGISTRATION TEST</title>
</head>
<body>
    <h1>🔥 DIRECT DATABASE REGISTRATION</h1>
    
    <form id="regForm">
        <div>
            <label>User ID:</label><br>
            <input type="text" id="userId" required><br><br>
        </div>
        
        <div>
            <label>Name:</label><br>
            <input type="text" id="name" required><br><br>
        </div>
        
        <div>
            <label>Email:</label><br>
            <input type="email" id="email" required><br><br>
        </div>
        
        <div>
            <label>Password:</label><br>
            <input type="password" id="password" required><br><br>
        </div>
        
        <div>
            <label>Date of Birth:</label><br>
            <input type="date" id="dateOfBirth" required><br><br>
        </div>
        
        <button type="submit">🔥 REGISTER NOW</button>
    </form>
    
    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>
    
    <script>
        document.getElementById('regForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const data = {
                userId: document.getElementById('userId').value,
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                dateOfBirth: document.getElementById('dateOfBirth').value
            };
            
            console.log('🔥 POSTING:', data);
            document.getElementById('result').innerHTML = '🔄 Posting to database...';
            
            try {
                const response = await fetch('http://localhost:5000/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                console.log('📥 RESULT:', result);
                
                if (result.success) {
                    document.getElementById('result').innerHTML = '✅ SUCCESS: ' + result.message;
                    document.getElementById('result').style.backgroundColor = '#d4edda';
                } else {
                    document.getElementById('result').innerHTML = '❌ ERROR: ' + result.message;
                    document.getElementById('result').style.backgroundColor = '#f8d7da';
                }
                
            } catch (error) {
                console.error('🔥 ERROR:', error);
                document.getElementById('result').innerHTML = '❌ NETWORK ERROR: ' + error.message;
                document.getElementById('result').style.backgroundColor = '#f8d7da';
            }
        });
    </script>
</body>
</html>
