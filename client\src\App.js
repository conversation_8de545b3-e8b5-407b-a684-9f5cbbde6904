import React, { useState } from 'react';
import './App.css';

function App() {
  const [currentPage, setCurrentPage] = useState('register');
  const [users, setUsers] = useState([]); // Simple in-memory user storage
  const [currentUser, setCurrentUser] = useState(null);

  return (
    <div style={{ maxWidth: '500px', margin: '50px auto', padding: '20px' }}>
      <h1>🚀 MERN Authentication Demo</h1>

      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={() => setCurrentPage('register')}
          style={{
            marginRight: '10px',
            padding: '10px 20px',
            backgroundColor: currentPage === 'register' ? '#007bff' : '#ccc',
            color: 'white',
            border: 'none',
            borderRadius: '5px'
          }}
        >
          Register
        </button>
        <button
          onClick={() => setCurrentPage('login')}
          style={{
            marginRight: '10px',
            padding: '10px 20px',
            backgroundColor: currentPage === 'login' ? '#007bff' : '#ccc',
            color: 'white',
            border: 'none',
            borderRadius: '5px'
          }}
        >
          Login
        </button>
        {currentUser && (
          <button
            onClick={() => setCurrentPage('dashboard')}
            style={{
              padding: '10px 20px',
              backgroundColor: currentPage === 'dashboard' ? '#007bff' : '#ccc',
              color: 'white',
              border: 'none',
              borderRadius: '5px'
            }}
          >
            Dashboard
          </button>
        )}
      </div>

      {currentPage === 'register' && (
        <Register users={users} setUsers={setUsers} setCurrentPage={setCurrentPage} />
      )}
      {currentPage === 'login' && (
        <Login users={users} setCurrentUser={setCurrentUser} setCurrentPage={setCurrentPage} />
      )}
      {currentPage === 'dashboard' && currentUser && (
        <Dashboard currentUser={currentUser} setCurrentUser={setCurrentUser} setCurrentPage={setCurrentPage} />
      )}
    </div>
  );
}

// Register Component
function Register({ users, setUsers, setCurrentPage }) {
  const [formData, setFormData] = useState({
    userId: '', name: '', email: '', password: '', confirmPassword: '', dateOfBirth: ''
  });
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    if (formData.password !== formData.confirmPassword) {
      setMessage('❌ Passwords do not match');
      setLoading(false);
      return;
    }

    // Check if user already exists
    const existingUser = users.find(u => u.userId === formData.userId || u.email === formData.email);
    if (existingUser) {
      setMessage('❌ User already exists');
      setLoading(false);
      return;
    }

    // Create new user
    const newUser = {
      id: Date.now().toString(),
      userId: formData.userId,
      name: formData.name,
      email: formData.email,
      password: formData.password, // In real app, this would be hashed
      dateOfBirth: formData.dateOfBirth,
      createdAt: new Date().toISOString()
    };

    // Add user to storage
    setUsers([...users, newUser]);
    setMessage('✅ Registration successful! You can now login.');

    // Clear form
    setFormData({ userId: '', name: '', email: '', password: '', confirmPassword: '', dateOfBirth: '' });

    // Auto-switch to login after 2 seconds
    setTimeout(() => {
      setCurrentPage('login');
    }, 2000);
    setLoading(false);
  };

  return (
    <div>
      <h2>🚀 Register</h2>
      <p>Create your account below:</p>

      {message && (
        <div style={{
          padding: '10px', margin: '10px 0',
          backgroundColor: message.includes('✅') ? '#d4edda' : '#f8d7da',
          border: '1px solid ' + (message.includes('✅') ? '#c3e6cb' : '#f5c6cb'),
          borderRadius: '5px'
        }}>
          {message}
        </div>
      )}

      <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
        <input type="text" name="userId" placeholder="User ID" value={formData.userId} onChange={handleChange} required style={{ padding: '8px' }} />
        <input type="text" name="name" placeholder="Name" value={formData.name} onChange={handleChange} required style={{ padding: '8px' }} />
        <input type="email" name="email" placeholder="Email" value={formData.email} onChange={handleChange} required style={{ padding: '8px' }} />
        <input type="password" name="password" placeholder="Password" value={formData.password} onChange={handleChange} required style={{ padding: '8px' }} />
        <input type="password" name="confirmPassword" placeholder="Confirm Password" value={formData.confirmPassword} onChange={handleChange} required style={{ padding: '8px' }} />
        <input type="date" name="dateOfBirth" value={formData.dateOfBirth} onChange={handleChange} required style={{ padding: '8px' }} />
        <button type="submit" disabled={loading} style={{ padding: '12px', backgroundColor: loading ? '#ccc' : '#007bff', color: 'white', border: 'none', borderRadius: '5px' }}>
          {loading ? '🔄 Registering...' : '🚀 Register'}
        </button>
      </form>
    </div>
  );
}

// Login Component
function Login({ users, setCurrentUser, setCurrentPage }) {
  const [formData, setFormData] = useState({
    emailOrUserId: '',
    password: ''
  });
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    // Find user
    const user = users.find(u =>
      (u.email === formData.emailOrUserId || u.userId === formData.emailOrUserId) &&
      u.password === formData.password
    );

    if (user) {
      setCurrentUser(user);
      setMessage('✅ Login successful!');
      setFormData({ emailOrUserId: '', password: '' });
      setTimeout(() => {
        setCurrentPage('dashboard');
      }, 1000);
    } else {
      setMessage('❌ Invalid credentials');
    }

    setLoading(false);
  };

  return (
    <div>
      <h2>🔐 Login</h2>
      <p>Enter your credentials:</p>

      {message && (
        <div style={{
          padding: '10px', margin: '10px 0',
          backgroundColor: message.includes('✅') ? '#d4edda' : '#f8d7da',
          border: '1px solid ' + (message.includes('✅') ? '#c3e6cb' : '#f5c6cb'),
          borderRadius: '5px'
        }}>
          {message}
        </div>
      )}

      <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
        <input
          type="text"
          name="emailOrUserId"
          placeholder="Email or User ID"
          value={formData.emailOrUserId}
          onChange={handleChange}
          required
          style={{ padding: '8px' }}
        />
        <input
          type="password"
          name="password"
          placeholder="Password"
          value={formData.password}
          onChange={handleChange}
          required
          style={{ padding: '8px' }}
        />
        <button
          type="submit"
          disabled={loading}
          style={{
            padding: '12px',
            backgroundColor: loading ? '#ccc' : '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '5px'
          }}
        >
          {loading ? '🔄 Logging in...' : '🔐 Login'}
        </button>
      </form>
    </div>
  );
}

// Dashboard Component
function Dashboard({ currentUser, setCurrentUser, setCurrentPage }) {
  const handleLogout = () => {
    setCurrentUser(null);
    setCurrentPage('login');
  };

  return (
    <div>
      <h2>📊 Dashboard</h2>
      <div style={{
        padding: '20px',
        backgroundColor: '#f8f9fa',
        borderRadius: '5px',
        marginBottom: '20px'
      }}>
        <h3>Welcome, {currentUser.name}! 🎉</h3>
        <p><strong>User ID:</strong> {currentUser.userId}</p>
        <p><strong>Email:</strong> {currentUser.email}</p>
        <p><strong>Date of Birth:</strong> {currentUser.dateOfBirth}</p>
        <p><strong>Account Created:</strong> {new Date(currentUser.createdAt).toLocaleDateString()}</p>
      </div>

      <button
        onClick={handleLogout}
        style={{
          padding: '10px 20px',
          backgroundColor: '#dc3545',
          color: 'white',
          border: 'none',
          borderRadius: '5px'
        }}
      >
        🚪 Logout
      </button>
    </div>
  );
}

export default App;
