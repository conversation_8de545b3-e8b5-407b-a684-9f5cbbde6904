import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, Link } from 'react-router-dom';
import './App.css';

// Simple Authentication Context
const AuthContext = React.createContext();

function App() {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token'));

  useEffect(() => {
    if (token) {
      // Decode token to get user info (simple approach)
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        setUser(payload);
      } catch (error) {
        localStorage.removeItem('token');
        setToken(null);
      }
    }
  }, [token]);

  const login = (token, userData) => {
    localStorage.setItem('token', token);
    setToken(token);
    setUser(userData);
  };

  const logout = () => {
    localStorage.removeItem('token');
    setToken(null);
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, token, login, logout }}>
      <Router>
        <div style={{ maxWidth: '500px', margin: '50px auto', padding: '20px' }}>
          <Routes>
            <Route path="/register" element={<Register />} />
            <Route path="/login" element={<Login />} />
            <Route path="/dashboard" element={
              token ? <Dashboard /> : <Navigate to="/login" />
            } />
            <Route path="/" element={<Navigate to={token ? "/dashboard" : "/login"} />} />
          </Routes>
        </div>
      </Router>
    </AuthContext.Provider>
  );
}

// Register Component
function Register() {
  const [formData, setFormData] = useState({
    userId: '', name: '', email: '', password: '', confirmPassword: '', dateOfBirth: ''
  });
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    if (formData.password !== formData.confirmPassword) {
      setMessage('❌ Passwords do not match');
      setLoading(false);
      return;
    }

    const postData = {
      userId: formData.userId, name: formData.name, email: formData.email,
      password: formData.password, dateOfBirth: formData.dateOfBirth
    };

    try {
      const response = await fetch('http://localhost:3001/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(postData)
      });

      const result = await response.json();
      if (result.success) {
        setMessage('✅ Registration successful! You can now login.');
        setFormData({ userId: '', name: '', email: '', password: '', confirmPassword: '', dateOfBirth: '' });
      } else {
        setMessage('❌ ' + result.message);
      }
    } catch (error) {
      setMessage('❌ Network error: ' + error.message);
    }
    setLoading(false);
  };

  return (
    <div>
      <h1>🚀 Register</h1>
      <p><Link to="/login">Already have an account? Login</Link></p>

      {message && (
        <div style={{
          padding: '10px', margin: '10px 0',
          backgroundColor: message.includes('✅') ? '#d4edda' : '#f8d7da',
          border: '1px solid ' + (message.includes('✅') ? '#c3e6cb' : '#f5c6cb'),
          borderRadius: '5px'
        }}>
          {message}
        </div>
      )}

      <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
        <input type="text" name="userId" placeholder="User ID" value={formData.userId} onChange={handleChange} required style={{ padding: '8px' }} />
        <input type="text" name="name" placeholder="Name" value={formData.name} onChange={handleChange} required style={{ padding: '8px' }} />
        <input type="email" name="email" placeholder="Email" value={formData.email} onChange={handleChange} required style={{ padding: '8px' }} />
        <input type="password" name="password" placeholder="Password" value={formData.password} onChange={handleChange} required style={{ padding: '8px' }} />
        <input type="password" name="confirmPassword" placeholder="Confirm Password" value={formData.confirmPassword} onChange={handleChange} required style={{ padding: '8px' }} />
        <input type="date" name="dateOfBirth" value={formData.dateOfBirth} onChange={handleChange} required style={{ padding: '8px' }} />
        <button type="submit" disabled={loading} style={{ padding: '12px', backgroundColor: loading ? '#ccc' : '#007bff', color: 'white', border: 'none', borderRadius: '5px' }}>
          {loading ? '🔄 Registering...' : '🚀 Register'}
        </button>
      </form>
    </div>
  );
}
