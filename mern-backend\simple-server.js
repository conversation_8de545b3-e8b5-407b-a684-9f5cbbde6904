const express = require("express");
const cors = require("cors");

console.log("🚀 STARTING SIMPLE SERVER...");

const app = express();

// Middleware
app.use(cors({
  origin: true,
  credentials: true
}));
app.use(express.json());

// Health check
app.get("/", (req, res) => {
  console.log("📥 Health check request received");
  res.json({ message: "🚀 SIMPLE SERVER RUNNING!" });
});

// Test registration endpoint (without MongoDB)
app.post("/register", (req, res) => {
  console.log("📤 Registration request:", req.body);
  
  const { userId, name, email, password, dateOfBirth } = req.body;
  
  // Simple validation
  if (!userId || !name || !email || !password || !dateOfBirth) {
    return res.status(400).json({
      success: false,
      message: "All fields are required"
    });
  }
  
  // Mock successful registration
  res.json({
    success: true,
    message: "User registered successfully! (Mock response)",
    user: {
      userId,
      name,
      email
    }
  });
});

// Test login endpoint (without MongoDB)
app.post("/login", (req, res) => {
  console.log("🔐 Login request:", req.body);
  
  const { emailOrUserId, password } = req.body;
  
  if (!emailOrUserId || !password) {
    return res.status(400).json({
      success: false,
      message: "Email/UserID and password are required"
    });
  }
  
  // Mock successful login
  res.json({
    success: true,
    message: "Login successful! (Mock response)",
    token: "mock-jwt-token-12345",
    user: {
      userId: "mockuser",
      name: "Mock User",
      email: emailOrUserId
    }
  });
});

// Start server
const PORT = 3001;
app.listen(PORT, () => {
  console.log(`🚀 SIMPLE SERVER RUNNING ON PORT ${PORT}`);
  console.log(`🌐 Health: http://localhost:${PORT}/`);
  console.log(`📤 Register: http://localhost:${PORT}/register`);
  console.log(`🔐 Login: http://localhost:${PORT}/login`);
});
