const express = require("express");
const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const cors = require("cors");
require("dotenv").config();

console.log("🚀 STARTING SERVER...");
console.log("🔗 MongoDB URI:", process.env.MONGO_URI ? "Found" : "Missing");
console.log("🔑 JWT Secret:", process.env.JWT_SECRET ? "Found" : "Missing");

const app = express();

// Middleware - Allow all origins for testing
app.use(cors({
  origin: true,
  credentials: true
}));
app.use(express.json());

// Connect to MongoDB
console.log("🔄 Attempting MongoDB connection...");
mongoose.connect(process.env.MONGO_URI)
.then(() => {
  console.log("✅ MongoDB connected successfully!");
})
.catch(err => {
  console.error("❌ MongoDB connection failed:", err.message);
  console.error("❌ Full error:", err);
  // Don't exit the process, continue without MongoDB for testing
});

// Simple User Schema
const userSchema = new mongoose.Schema({
  userId: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  dateOfBirth: { type: Date, required: true }
}, { timestamps: true });

const User = mongoose.model("User", userSchema);

// JWT Authentication Middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access token required'
    });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }
    req.user = user;
    next();
  });
};

// Health check
app.get("/", (req, res) => {
  res.json({ message: "🚀 SERVER RUNNING!" });
});

// Registration endpoint
app.post("/register", async (req, res) => {
  console.log("📤 Registration request:", req.body);
  
  try {
    const { userId, name, email, password, dateOfBirth } = req.body;
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);
    
    // Create user
    const newUser = new User({
      userId,
      name,
      email,
      password: hashedPassword,
      dateOfBirth: new Date(dateOfBirth)
    });
    
    // Save to database
    const savedUser = await newUser.save();
    
    console.log("✅ User saved:", savedUser.userId);
    
    res.json({
      success: true,
      message: "User registered successfully!",
      user: {
        id: savedUser._id,
        userId: savedUser.userId,
        name: savedUser.name,
        email: savedUser.email
      }
    });
    
  } catch (error) {
    console.error("❌ Registration failed:", error.message);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "User already exists"
      });
    }
    
    res.status(500).json({
      success: false,
      message: "Registration failed: " + error.message
    });
  }
});

// Login endpoint
app.post("/login", async (req, res) => {
  console.log("🔐 Login request:", req.body);

  try {
    const { emailOrUserId, password } = req.body;

    if (!emailOrUserId || !password) {
      return res.status(400).json({
        success: false,
        message: "Email/UserID and password are required"
      });
    }

    // Find user by email or userId
    const user = await User.findOne({
      $or: [
        { email: emailOrUserId },
        { userId: emailOrUserId }
      ]
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials"
      });
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials"
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user.userId,
        email: user.email,
        name: user.name,
        id: user._id
      },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    console.log("✅ Login successful:", user.userId);

    res.json({
      success: true,
      message: "Login successful!",
      token: token,
      user: {
        id: user._id,
        userId: user.userId,
        name: user.name,
        email: user.email
      }
    });

  } catch (error) {
    console.error("❌ Login failed:", error.message);
    res.status(500).json({
      success: false,
      message: "Login failed: " + error.message
    });
  }
});

// Dashboard endpoint (protected)
app.get("/dashboard", authenticateToken, async (req, res) => {
  console.log("📊 Dashboard request from user:", req.user.userId);

  try {
    // Fetch fresh user data from database
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found"
      });
    }

    console.log("✅ Dashboard data sent for:", user.name);

    res.json({
      success: true,
      message: `Welcome ${user.name}!`,
      user: {
        id: user._id,
        userId: user.userId,
        name: user.name,
        email: user.email,
        dateOfBirth: user.dateOfBirth,
        createdAt: user.createdAt
      }
    });

  } catch (error) {
    console.error("❌ Dashboard error:", error.message);
    res.status(500).json({
      success: false,
      message: "Failed to load dashboard: " + error.message
    });
  }
});

// Start server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`🚀 SERVER RUNNING ON PORT ${PORT}`);
  console.log(`🌐 Health: http://localhost:${PORT}/`);
  console.log(`📤 Register: http://localhost:${PORT}/register`);
  console.log(`🔐 Login: http://localhost:${PORT}/login`);
  console.log(`📊 Dashboard: http://localhost:${PORT}/dashboard`);
});
