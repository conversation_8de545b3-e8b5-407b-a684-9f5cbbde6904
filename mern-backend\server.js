const express = require("express");
const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const cors = require("cors");
require("dotenv").config();

console.log("🚀 STARTING SERVER...");

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI)
.then(() => console.log("✅ MongoDB connected"))
.catch(err => console.error("❌ MongoDB error:", err));

// User Schema
const userSchema = new mongoose.Schema({
  userId: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  dateOfBirth: { type: Date, required: true }
}, { timestamps: true });

const User = mongoose.model("User", userSchema);

// Health check
app.get("/", (req, res) => {
  res.json({ message: "🚀 SERVER RUNNING!" });
});

// Registration endpoint
app.post("/register", async (req, res) => {
  console.log("📤 Registration request:", req.body);
  
  try {
    const { userId, name, email, password, dateOfBirth } = req.body;
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);
    
    // Create user
    const newUser = new User({
      userId,
      name,
      email,
      password: hashedPassword,
      dateOfBirth: new Date(dateOfBirth)
    });
    
    // Save to database
    const savedUser = await newUser.save();
    
    console.log("✅ User saved:", savedUser.userId);
    
    res.json({
      success: true,
      message: "User registered successfully!",
      user: {
        id: savedUser._id,
        userId: savedUser.userId,
        name: savedUser.name,
        email: savedUser.email
      }
    });
    
  } catch (error) {
    console.error("❌ Registration failed:", error.message);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "User already exists"
      });
    }
    
    res.status(500).json({
      success: false,
      message: "Registration failed: " + error.message
    });
  }
});

// Start server
const PORT = 5000;
app.listen(PORT, () => {
  console.log(`🚀 SERVER RUNNING ON PORT ${PORT}`);
  console.log(`🌐 Health: http://localhost:${PORT}/`);
  console.log(`📤 Register: http://localhost:${PORT}/register`);
});
