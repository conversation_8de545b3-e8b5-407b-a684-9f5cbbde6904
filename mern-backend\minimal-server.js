const http = require('http');

console.log("🚀 STARTING MINIMAL SERVER...");

const server = http.createServer((req, res) => {
  console.log(`📥 ${req.method} ${req.url}`);
  
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  // Set content type
  res.setHeader('Content-Type', 'application/json');
  
  if (req.method === 'GET' && req.url === '/') {
    res.writeHead(200);
    res.end(JSON.stringify({ message: "🚀 MINIMAL SERVER RUNNING!" }));
  } else if (req.method === 'POST' && req.url === '/register') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log("📤 Registration data:", data);
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          message: "Registration successful! (Minimal server)",
          user: {
            userId: data.userId,
            name: data.name,
            email: data.email
          }
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, message: "Invalid JSON" }));
      }
    });
  } else {
    res.writeHead(404);
    res.end(JSON.stringify({ message: "Not found" }));
  }
});

const PORT = 3001;
server.listen(PORT, () => {
  console.log(`🚀 MINIMAL SERVER RUNNING ON PORT ${PORT}`);
  console.log(`🌐 Health: http://localhost:${PORT}/`);
  console.log(`📤 Register: http://localhost:${PORT}/register`);
});

server.on('error', (error) => {
  console.error('❌ Server error:', error);
});
